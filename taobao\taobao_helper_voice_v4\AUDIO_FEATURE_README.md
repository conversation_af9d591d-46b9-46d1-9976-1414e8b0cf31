# 手卡音频播放功能说明

## 功能概述

在手卡显示的modal上新增了播放音频区域，用户可以播放与当前讲解产品相关的音频文件。

## 新增功能特性

### 1. 音频播放区域
- 位置：手卡modal的顶部，标题栏下方
- 背景色：浅灰色 (#f8f9fa)
- 包含播放按钮、进度条、时间显示和状态显示

### 2. 播放按钮
- 默认显示：▶️ 播放
- 播放时显示：⏸️ 暂停
- 颜色变化：
  - 默认：蓝色 (#007bff)
  - 播放中：红色 (#dc3545)
  - 悬停效果：深蓝色 (#0056b3)

### 3. 进度条
- 实时显示播放进度
- 支持点击跳转到指定位置
- 蓝色填充条显示当前进度

### 4. 时间显示
- 格式：MM:SS / MM:SS (当前时间 / 总时长)
- 实时更新当前播放时间

### 5. 状态显示
- 加载中：黄色文字 "加载中..."
- 准备就绪：绿色文字 "准备就绪"
- 播放中：蓝色文字 "播放中"
- 已暂停：灰色文字 "已暂停"
- 已播放完毕：绿色文字 "已播放完毕"
- 播放失败：红色文字 "播放失败"
- 无音频文件：灰色文字 "无音频文件"

## 音频文件路径规则

音频文件路径格式：`D:/voice/itemId.wav`

其中：
- `itemId` 是当前讲解产品的商品ID
- 文件扩展名固定为 `.wav`

例如：
- 商品ID为 `12345` 的音频文件路径：`D:/voice/12345.wav`
- 商品ID为 `67890` 的音频文件路径：`D:/voice/67890.wav`

## 代码修改说明

### 1. CustomCardModalManager类扩展

#### 新增属性
```javascript
this.audioSection        // 音频播放区域容器
this.playButton         // 播放/暂停按钮
this.progressContainer  // 进度条容器
this.progressBar        // 进度条背景
this.progressFill       // 进度条填充
this.timeDisplay        // 时间显示
this.statusDisplay      // 状态显示
this.audioElement       // HTML5 audio元素
this.currentItemId      // 当前商品ID
this.audioPath          // 音频文件路径
this.isPlaying          // 播放状态标记
```

#### 新增方法
```javascript
setAudioPath(itemId)           // 设置音频文件路径
toggleAudio()                  // 切换播放/暂停
stopAudio()                    // 停止播放
updateTimeDisplay(current, duration) // 更新时间显示
```

### 2. 事件处理

#### 音频事件监听
- `loadstart`: 开始加载音频
- `canplay`: 音频可以播放
- `play`: 开始播放
- `pause`: 暂停播放
- `ended`: 播放结束
- `timeupdate`: 播放时间更新
- `error`: 播放错误

#### 用户交互事件
- 播放按钮点击：切换播放/暂停状态
- 进度条点击：跳转到指定播放位置
- modal关闭：自动停止音频播放

### 3. 集成到现有流程

在 `showCustomCard` 函数中添加了音频路径设置：

```javascript
// 设置音频路径（使用商品ID）
if (productInfo.itemId) {
    cardClient.modal.setAudioPath(productInfo.itemId);
}
```

## 使用方法

1. 确保音频文件存在于 `D:/voice/` 目录下
2. 音频文件命名格式为 `{商品ID}.wav`
3. 点击"开始讲解"或"再次讲解"按钮触发手卡显示
4. 在弹出的手卡modal中，点击播放按钮开始播放音频
5. 使用进度条控制播放进度
6. 关闭modal时音频会自动停止

## 注意事项

1. 音频文件必须是 `.wav` 格式
2. 音频文件路径固定为 `D:/voice/` 目录
3. 如果音频文件不存在，会显示"播放失败"状态
4. 关闭手卡modal时会自动停止音频播放
5. 支持的音频操作：播放、暂停、跳转、显示进度

## 测试文件

创建了 `test_audio.html` 文件用于功能测试，包含：
- 功能说明
- 测试步骤
- 模拟数据
- 测试按钮

可以在浏览器中打开此文件进行功能验证。
