<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频播放功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .mock-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>音频播放功能测试</h1>
        
        <div class="info">
            <h3>功能说明：</h3>
            <ul>
                <li>在手卡显示的modal上新增了播放音频区域</li>
                <li>添加了播放按钮，点击后播放 D:/voice/itemId.wav</li>
                <li>显示播放进度条和时间</li>
                <li>播放完成后显示"已播放完毕"</li>
                <li>itemId.wav 代表当前讲解产品的id+后缀</li>
            </ul>
        </div>

        <h3>测试步骤：</h3>
        <ol>
            <li>点击下面的"测试手卡显示"按钮</li>
            <li>在弹出的手卡modal中，查看音频播放区域</li>
            <li>点击播放按钮测试音频播放功能</li>
            <li>观察进度条和状态显示</li>
        </ol>

        <div class="mock-item">
            <h4>模拟商品项 (itemId: 782366536941)</h4>
            <div class="list-item-content-title-item-name">测试商品标题</div>
            <div class="calculated-commission">佣金:￥10.50</div>
            <div data-rbd-drag-handle-draggable-id="782366536941"></div>
        </div>

        <button class="test-button" onclick="testCardModal()">测试手卡显示</button>
        <button class="test-button" onclick="testAudioPath()">测试音频路径设置</button>
        
        <div id="test-results" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; display: none;">
            <h4>测试结果：</h4>
            <div id="result-content"></div>
        </div>
    </div>

    <script>
        // 模拟必要的全局函数和变量
        let enableLogging = true;
        
        function logDebug(message, data) {
            if (data !== undefined) {
                console.log(`[DEBUG] ${message}`, data);
            } else {
                console.log(`[DEBUG] ${message}`);
            }
        }

        // 模拟sendClearProtocol函数
        function sendClearProtocol() {
            console.log('调用清除协议');
        }

        // 模拟getCurrentLiveId函数
        function getCurrentLiveId() {
            return 'test-live-id-12345';
        }

        // 测试手卡modal显示
        function testCardModal() {
            // 创建模拟的listItem
            const mockListItem = document.querySelector('.mock-item');
            
            // 模拟showCustomCard函数的部分逻辑
            const liveId = getCurrentLiveId();
            
            // 清理之前的modal
            const existingModal = document.getElementById('custom-card-modal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // 创建CustomCardModalManager实例进行测试
            const modal = new CustomCardModalManager();
            
            // 设置测试音频路径
            modal.setAudioPath('782366536941');
            
            // 更新标题
            modal.updateTitle({
                title: '测试商品标题',
                commission: '佣金:￥10.50'
            });
            
            // 显示modal
            modal.show();
            
            // 模拟手卡内容
            setTimeout(() => {
                modal.updateCardInfo('这是一个测试手卡内容。\n\n产品特点：\n1. 高质量材料\n2. 精美设计\n3. 超值价格\n\n推荐理由：\n- 性价比高\n- 用户评价好\n- 售后服务完善', false, false, true);
            }, 1000);
            
            showTestResult('手卡modal已显示，请查看音频播放功能');
        }

        // 测试音频路径设置
        function testAudioPath() {
            const testItemId = '782366536941';
            const expectedPath = `D:/voice/${testItemId}.wav`;
            
            showTestResult(`测试音频路径设置：<br>商品ID: ${testItemId}<br>音频路径: ${expectedPath}`);
        }

        // 显示测试结果
        function showTestResult(message) {
            const resultsDiv = document.getElementById('test-results');
            const contentDiv = document.getElementById('result-content');
            contentDiv.innerHTML = message;
            resultsDiv.style.display = 'block';
        }

        // 在页面加载完成后，确保必要的函数可用
        window.addEventListener('load', function() {
            console.log('测试页面加载完成');
        });
    </script>
    
    <!-- 引入修改后的content.js中的相关类 -->
    <script>
        // 这里需要复制CustomCardModalManager类的定义
        // 由于文件太大，这里只做演示用途
        console.log('如需完整测试，请在实际环境中运行');
    </script>
</body>
</html>
