let enableLogging = false;
// 添加调试级别日志记录
function logDebug(message, data) {
  if (!enableLogging) return;
    if (data !== undefined) {
        console.log(`[DEBUG] ${message}`, data);
    } else {
        console.log(`[DEBUG] ${message}`);
    }
}


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
(function () {
	// 全局变量以跟踪是否已安装各种事件监听器
	let explainButtonMonitorInstalled = false;
	let closeButtonMonitorInstalled = false;
	let confirmDialogMonitorInstalled = false;
	// 新增：全局初始化状态标记，防止重复初始化
	let isInitialized = false;

	// 记录最近处理的按钮，防止重复处理
	let lastHandledExplainButton = null;
	let isHandlingExplainClick = false;

	// 新增：获取产品图片并调用桌面软件协议的方法
	function getProductImageAndOpenDesktopApp(listItem) {
		try {
			// 查找图片元素
			const imgElement = listItem.querySelector('.list-item-img-inner');
			if (!imgElement) {
				logDebug('未找到产品图片元素');
				return;
			}

			// 获取图片链接
			let imageUrl = imgElement.src;
			if (!imageUrl) {
				logDebug('未找到产品图片链接');
				return;
			}

			// 如果是相对链接，转换为绝对链接
			if (imageUrl.startsWith('//')) {
				imageUrl = 'https:' + imageUrl;
			}

			logDebug('获取到产品图片链接:', imageUrl);

			// 构建协议URL
			const protocolUrl = `tbviewer://?web_img=@${imageUrl}`;
			logDebug('准备调用协议:', protocolUrl);

			// 向background script发送消息来处理协议调用
			chrome.runtime.sendMessage({
				action: "openProtocol",
				url: protocolUrl
			}, function(response) {
				if (chrome.runtime.lastError) {
					logDebug('协议调用失败:', chrome.runtime.lastError.message);
				} else {
					logDebug('协议调用成功');
				}
			});

		} catch (error) {
			console.error('获取产品图片并调用协议时出错:', error);
		}
	}

	// 初始化时安装各种功能
	function initializeFeatures() {
		// 添加全局防护机制，防止重复初始化
		if (isInitialized) {
			logDebug('功能已初始化，跳过重复初始化');
			return;
		}

		logDebug('开始初始化功能模块...');
		installExplainButtonMonitor();
		installCloseButtonMonitor();
		installConfirmDialogAutoConfirm(); // 添加自动确认对话框
		
		// 标记为已初始化
		isInitialized = true;
		logDebug('所有功能模块初始化完成');
	}

	// 优化后的统一初始化逻辑
	function initializeWhenReady() {
		if (document.readyState === 'loading') {
			// 如果文档还在加载中，等待DOMContentLoaded事件
			document.addEventListener('DOMContentLoaded', initializeFeatures, { once: true });
		} else {
			// 如果文档已经加载完成，直接初始化
			initializeFeatures();
		}
	}

	// 使用MutationObserver监听DOM变化，但仅在必要时处理
	const observer = new MutationObserver(function (mutations) {
		let hasRelevantChanges = false;

		for (let i = 0; i < mutations.length; i++) {
			const mutation = mutations[i];
			// 只有当新元素添加到DOM时才需要检查
			if (mutation.addedNodes && mutation.addedNodes.length > 0) {
				// 检查是否添加了可能包含讲解按钮或需要AI手卡按钮的元素
				for (let j = 0; j < mutation.addedNodes.length; j++) {
					const node = mutation.addedNodes[j];
					if (node.nodeType === 1) { // 元素节点
						if (node.classList && (
							node.classList.contains('list-item') ||
							node.classList.contains('lak-new-script-card-bottom-aside')
						)) {
							hasRelevantChanges = true;
							break;
						}
						// 只在必要时查询子元素
						if (!hasRelevantChanges && (
							node.querySelector && (
								node.querySelector('.list-item') ||
								node.querySelector('#mark-time-point')
							)
						)) {
							hasRelevantChanges = true;
							break;
						}
					}
				}
				if (hasRelevantChanges) break;
			}
		}
	});

	// 设置观察选项，仅监听需要的变化
	observer.observe(document.body, {
		childList: true,  // 监听子节点添加或删除
		subtree: true,    // 监听整个子树
		attributes: false  // 不监听属性变化，减少不必要的触发
	});

	// 调用统一的初始化函数
	initializeWhenReady();

	// 监听"开始讲解"按钮的点击并自动触发AI手卡按钮
	function installExplainButtonMonitor() {
		// 如果已经安装了监控，就不重复安装
		if (explainButtonMonitorInstalled) return;

		// 使用事件委托监听整个文档
		document.addEventListener('click', function (e) {
			// 如果正在处理讲解按钮点击，跳过
			if (isHandlingExplainClick) return;

			// 查找点击事件是否命中"开始讲解"或"再次讲解"按钮
			let target = e.target;
			let buttonElement = null;

			// 向上遍历DOM树，查找按钮
			while (target && target !== document) {
				// 检查是否是讲解按钮 - 通过文本内容和样式类判断
				if (target.tagName === 'BUTTON' &&
					target.closest('#mark-time-point') &&
					(target.textContent.includes('开始讲解') || target.textContent.includes('再次讲解'))) {

					buttonElement = target;
					break;
				}
				target = target.parentNode;
			}

			// 如果点击的是讲解按钮并且不是最近处理过的按钮
			if (buttonElement && buttonElement !== lastHandledExplainButton) {
				// 标记正在处理讲解按钮点击
				isHandlingExplainClick = true;
				// 记录当前按钮为最近处理的按钮
				lastHandledExplainButton = buttonElement;

				// 找到商品项容器 - 修复：查找包含data-rbd-draggable-id的元素
				let listItem = buttonElement.closest('.list-item');
				if (!listItem) {
					isHandlingExplainClick = false;
					return;
				}

				// 查找包含data-rbd-draggable-id的父级元素
				let draggableElement = listItem.closest('[data-rbd-draggable-id]');
				if (draggableElement) {
					// 如果找到包含data-rbd-draggable-id的元素，使用它作为listItem
					listItem = draggableElement;
					logDebug('找到包含data-rbd-draggable-id的元素:', draggableElement);
					logDebug('data-rbd-draggable-id值:', draggableElement.getAttribute('data-rbd-draggable-id'));
				} else {
					// 如果没找到，检查listItem本身是否有data-rbd-draggable-id
					if (!listItem.hasAttribute('data-rbd-draggable-id')) {
						logDebug('警告：未找到包含data-rbd-draggable-id的元素，使用默认的list-item');
					}
				}

				// 新增：获取产品图片并调用桌面软件
				getProductImageAndOpenDesktopApp(listItem);

				// 让原始的讲解按钮点击事件执行完成
				setTimeout(function () {
					// 调用自定义手卡方法而不是点击AI手卡按钮
					if (window.showCustomCard) {
						window.showCustomCard(listItem);
					} else {
						// 如果自定义手卡方法未加载，则回退到原来的方式
						const smartCardBtn = listItem.querySelector('.smart-card-btn');
						if (smartCardBtn) {
							smartCardBtn.click();
						}
					}

					// 重置处理状态
					setTimeout(() => {
						isHandlingExplainClick = false;
					}, 500);
				}, 800); // 延迟，确保原始点击事件已完全处理
			}
		}, true); // 使用捕获模式

		// 标记为已安装
		explainButtonMonitorInstalled = true;
	}


	// 安装自动确认对话框功能
	function installConfirmDialogAutoConfirm() {
		if (confirmDialogMonitorInstalled) return;

		// 创建一个新的MutationObserver来监视对话框的出现
		const dialogObserver = new MutationObserver(function (mutations) {
			mutations.forEach(function (mutation) {
				if (mutation.addedNodes && mutation.addedNodes.length > 0) {
					// 检查是否添加了模态对话框
					for (let i = 0; i < mutation.addedNodes.length; i++) {
						const node = mutation.addedNodes[i];
						if (node.nodeType === 1) { // 元素节点
							// 检查是否是我们要处理的确认对话框
							checkAndConfirmDialog(node);
						}
					}
				}
			});
		});

		// 监听整个body以捕获对话框的添加
		dialogObserver.observe(document.body, {
			childList: true,
			subtree: true
		});

		// 标记为已安装
		confirmDialogMonitorInstalled = true;
		logDebug('已安装自动确认对话框功能');

		// 立即检查一次，防止已经存在的对话框
		checkForExistingDialogs();
	}

	// 检查并自动确认对话框
	function checkAndConfirmDialog(node) {
		// 检查是否是模态对话框内容
		if (node.classList && node.classList.contains('tbla-modal-content')) {
			// 检查对话框内容是否包含确认文本
			const modalBody = node.querySelector('.tbla-modal-body');

			if (modalBody) {
				const modalContent = modalBody.textContent || '';

				// 情况1: 当前商品正在讲解中的确认框
				if (modalContent.includes('正在讲解中') && modalContent.includes('确定开始新的讲解吗')) {
					logDebug('找到讲解确认对话框，自动点击确认按钮');
					clickConfirmButton(node);
				}
				// 情况2: 讲解未满20秒的确认框
				else if (modalContent.includes('讲解未满20秒') && modalContent.includes('将不会保存此直播讲解')) {
					logDebug('找到讲解未满20秒确认对话框，自动点击确认按钮');
					clickConfirmButton(node);
				}
				// 情况3: 任何确认对话框内容包含"确定"按钮的
				else {
					const confirmContent = node.querySelector('.tbla-modal-confirm-content');
					if (confirmContent) {
						logDebug('找到其他确认对话框，自动点击确认按钮');
						clickConfirmButton(node);
					}
				}
			}
		} else {
			// 如果当前节点不是对话框，检查其子节点
			const dialogContent = node.querySelector('.tbla-modal-content');
			if (dialogContent) {
				checkAndConfirmDialog(dialogContent);
			}
		}
	}

	// 辅助函数：点击确认按钮
	function clickConfirmButton(dialogNode) {
		// 找到确认按钮并点击
		const confirmButton = dialogNode.querySelector('.tbla-btn-primary');
		if (confirmButton) {
			// 延迟点击，让对话框完全显示
			setTimeout(() => {
				confirmButton.click();
				logDebug('已自动点击确认按钮');
			}, 100);
		}
	}

	// 检查页面上是否已经存在确认对话框
	function checkForExistingDialogs() {
		const dialogs = document.querySelectorAll('.tbla-modal-content');
		dialogs.forEach(dialog => {
			checkAndConfirmDialog(dialog);
		});
	}

	// 新增：发送清除协议的方法
	function sendClearProtocol() {
		try {
			const protocolUrl = 'tbviewer://?clear=true';
			logDebug('发送清除协议:', protocolUrl);

			// 向background script发送消息来处理协议调用
			chrome.runtime.sendMessage({
				action: "openProtocol",
				url: protocolUrl
			}, function(response) {
				if (chrome.runtime.lastError) {
					logDebug('清除协议调用失败:', chrome.runtime.lastError.message);
				} else {
					logDebug('清除协议调用成功');
				}
			});

		} catch (error) {
			console.error('发送清除协议时出错:', error);
		}
	}
////////////////////////////////////////////////////////////////////////关闭按钮监控////////////////////////////////////////////////////////////////////////
	// 添加关闭按钮监控
	function installCloseButtonMonitor() {
		// 如果已经安装了监控，就不重复安装
		if (closeButtonMonitorInstalled) return;

		// 使用事件委托方式在document上监听点击事件
		document.addEventListener('click', function (e) {
			// 查找点击事件是否命中关闭按钮或其子元素
			let target = e.target;
			let isCloseButton = false;

			// 向上遍历DOM树，检查是否是关闭按钮或其子元素
			while (target && target !== document) {
				// 检查元素是否是关闭按钮
				if (target.classList && (
					target.classList.contains('tbla-drawer-close') ||
					(target.getAttribute('aria-label') === 'Close') ||
					(target.classList.contains('anticon-close'))
				)) {
					isCloseButton = true;
					break;
				}
				target = target.parentNode;
			}

			// 如果是关闭按钮，发送清除协议并跳过确认框直接关闭
			if (isCloseButton) {
				// 立即发送清除协议
				sendClearProtocol();

				// 延迟执行，等待原始的点击事件完成
				setTimeout(function () {
					// 查找确认对话框
					const confirmDialog = document.querySelector('.tbla-modal-content');
					if (confirmDialog) {
						// 找到并点击"确认关闭"按钮
						const confirmButton = document.querySelector('.tbla-modal-confirm-btns .tbla-btn-primary');
						if (confirmButton) {
							confirmButton.click();

							// 关闭后重置状态
							setTimeout(() => {
								isHandlingExplainClick = false;
							}, 500);
						}
					} else {
						// 即使没有确认对话框，也尝试重置状态
						setTimeout(() => {
							isHandlingExplainClick = false;
						}, 500);
					}
				}, 50);
			}
		}, true); // 使用捕获模式以确保在事件冒泡之前处理

		// 标记为已安装
		closeButtonMonitorInstalled = true;
	}

	// 将sendClearProtocol方法暴露到全局作用域
	window.sendClearProtocol = sendClearProtocol;
})();
// ///////////////////////////////////////////////////////   录制时间悬浮窗  ///////////////////////////////////////////////////////////////
(function () {
	// 删除可能已存在的悬浮窗和样式
	function cleanupExisting() {
		// 移除可能的旧悬浮窗
		const oldTimer = document.getElementById('custom-recording-timer');
		if (oldTimer) {
			oldTimer.remove();
		}

		// 移除可能的旧样式
		const oldStyle = document.getElementById('custom-recording-timer-style');
		if (oldStyle) {
			oldStyle.remove();
		}
	}

	// 清理旧元素
	cleanupExisting();

	// 创建自定义样式
	const styleEl = document.createElement('style');
	styleEl.id = 'custom-recording-timer-style';
	styleEl.textContent = `
        #custom-recording-timer {
            position: fixed;
            top: 15px;
            right: 20%;
            background-color: rgba(0, 0, 0, 0.75);
            color: white;
            font-size: 30px;
            font-weight: bold;
            padding: 6px 12px;
            border-radius: 4px;
            z-index: 999999;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            cursor: move;
            user-select: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Arial, sans-serif;
            line-height: 1;
            transition: background-color 0.3s;
        }
        
        #custom-recording-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #ff0000;
            margin-right: 8px;
            display: inline-block;
            animation: custom-recording-blink 1s infinite;
        }
        
        @keyframes custom-recording-blink {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    `;
	document.head.appendChild(styleEl);

	// 创建悬浮窗
	const timerEl = document.createElement('div');
	timerEl.id = 'custom-recording-timer';

	// 创建内部内容
	timerEl.innerHTML = `
        <div id="custom-recording-dot"></div>
        <div id="custom-recording-time">00:00:00</div>
    `;

	// 添加到页面
	document.body.appendChild(timerEl);

	// 实现拖动功能
	let isDragging = false;
	let offsetX, offsetY;

	timerEl.addEventListener('mousedown', function (e) {
		isDragging = true;
		offsetX = e.clientX - timerEl.getBoundingClientRect().left;
		offsetY = e.clientY - timerEl.getBoundingClientRect().top;
	});

	document.addEventListener('mousemove', function (e) {
		if (!isDragging) return;

		timerEl.style.left = (e.clientX - offsetX) + 'px';
		timerEl.style.top = (e.clientY - offsetY) + 'px';
		timerEl.style.right = 'auto'; // 清除right值，避免冲突
	});

	document.addEventListener('mouseup', function () {
		isDragging = false;
	});

	// 记录最后检测到的录制时间
	let lastRecordingTime = '';
	let isVisible = false;

	// 查找和更新录制时间
	function updateRecordingTime() {
		// 查找页面上的录制时间元素
		const countdownElements = document.querySelectorAll('[class*="countdown--"]');
		let timeFound = false;
		let recordingListItem = null; // 保存找到的正在录制的list-item

		// 首先在countdown元素中查找
		for (const el of countdownElements) {
			// 先检查是否包含"讲解中"或"录制中"的内容
			const isRecordingOrExplaining = el.textContent.includes('录制中') || el.textContent.includes('讲解中');
            
			// 查找内部的span元素 - 支持新旧两种结构
			let spans;
			// 新结构：在lak-space-item下查找span
			if (el.querySelector('.lak-space-item span')) {
				spans = el.querySelectorAll('.lak-space-item span');
			} else {
				// 旧结构：直接查找span
				spans = el.querySelectorAll('span');
			}
            
			for (const span of spans) {
				const text = span.textContent || '';

				// 检查是否包含时间格式 (00:00:00 或 00:00)
				if (/\d{2}:\d{2}(:\d{2})?/.test(text)) {
					const timeMatch = text.match(/(\d{2}:\d{2}(:\d{2})?)/);
					if (timeMatch) {
						const timeText = timeMatch[1];
						// 如果是短格式(MM:SS)，转为长格式(HH:MM:SS)
						const formattedTime = timeText.includes(':') && timeText.split(':').length === 2 
							? '00:' + timeText 
							: timeText;
                        
						if (formattedTime !== lastRecordingTime) {
							lastRecordingTime = formattedTime;
							document.getElementById('custom-recording-time').textContent = formattedTime;

							// 找到包含此元素的list-item
							if (isRecordingOrExplaining) {
								recordingListItem = el.closest('.list-item');
							}

							if (!isVisible) {
								timerEl.style.display = 'flex';
								isVisible = true;
								logDebug('显示录制/讲解计时器:', formattedTime);
							}
						}
						timeFound = true;
						break;
					}
				}
			}
			if (timeFound) break;
		}

		// 如果此时没有找到时间，但之前找到过，并且悬浮窗是可见的，可能需要隐藏
		if (!timeFound && isVisible) {
			logDebug('未找到录制/讲解时间，将隐藏计时器');
			timerEl.style.display = 'none';
			isVisible = false;
			lastRecordingTime = '';
		}
	}

	// 初始隐藏
	timerEl.style.display = 'none';

	// 设置定时检查
	setInterval(updateRecordingTime, 1000);
	// 查看页面上是否存在录制时间，如果有，立即更新
	updateRecordingTime();

	// 使用MutationObserver监听DOM变化
	const observer = new MutationObserver(function (mutations) {
		let needCheck = false;

		// 减少不必要的检查，只在可能有变化时检查
		for (const mutation of mutations) {
			if (mutation.type === 'childList' ||
				(mutation.type === 'attributes' && mutation.attributeName === 'class')) {
				needCheck = true;
				break;
			}
		}
		if (needCheck) {
			updateRecordingTime();
		}
	});

	// 开始监听
	observer.observe(document.body, {
		childList: true,
		subtree: true,
		attributes: true,
		attributeFilter: ['class']
	});
	logDebug('录制时间悬浮窗已创建');
})();
/////////////////////////////////////////////////////////////////////////佣金显示//////////////////////////////////////////////////////////////////////////////////////////////////////
(function () {
	// 保存已处理过的元素，避免重复处理
	const processedElements = new WeakSet();
	// 缓存选择器结果
	const selectors = {
		listContainer: null,
		// 使用Map缓存已查找的价格和佣金元素
		priceElementCache: new Map(),
		commissionElementCache: new Map(),
		groupElementCache: new Map()
	};

	// 优化防抖函数，添加最大等待时间限制
	function debounce(func, wait, maxWait = 1500) {
		let timeout;
		let lastCallTime = 0;

		return function () {
			const context = this;
			const args = arguments;
			const now = Date.now();

			// 如果超过最大等待时间，立即执行
			if (now - lastCallTime >= maxWait) {
				clearTimeout(timeout);
				lastCallTime = now;
				func.apply(context, args);
				return;
			}

			clearTimeout(timeout);
			lastCallTime = now;
			timeout = setTimeout(() => func.apply(context, args), wait);
		};
	}

	// 使用DocumentFragment优化DOM操作
	function createCommissionElement(commission, hasCommission) {
		const fragment = document.createDocumentFragment();
		const newItem = document.createElement('div');
		newItem.className = 'lak-space-item';
		
		const commissionDisplayElement = document.createElement('span');
		commissionDisplayElement.className = 'calculated-commission';
		commissionDisplayElement.style.cssText = 'margin-left:10px;font-weight:bold;font-size:18px;';
		
		if (hasCommission) {
			commissionDisplayElement.textContent = `佣金:￥${commission.toFixed(2)}`;
			commissionDisplayElement.style.color = 'red';
		} else {
			commissionDisplayElement.textContent = '无佣金';
			commissionDisplayElement.style.color = 'green';
		}
		
		newItem.appendChild(commissionDisplayElement);
		fragment.appendChild(newItem);
		return fragment;
	}

	// 查找DOM元素并缓存结果
	function findElement(item, selector, cacheMap) {
		if (!item) return null;
		
		// 使用自定义唯一ID作为缓存键
		if (!item.__uniqueId) {
			item.__uniqueId = Math.random().toString(36).substr(2, 9);
		}
		
		if (cacheMap.has(item.__uniqueId)) {
			return cacheMap.get(item.__uniqueId);
		}
		
		const element = item.querySelector(selector);
		if (element) {
			cacheMap.set(item.__uniqueId, element);
		}
		return element;
	}

	// 查找分组元素
	function findGroupElement(item) {
		if (!item) return null;
		
		// 使用缓存
		if (!item.__uniqueId) {
			item.__uniqueId = Math.random().toString(36).substr(2, 9);
		}
		
		if (selectors.groupElementCache.has(item.__uniqueId)) {
			return selectors.groupElementCache.get(item.__uniqueId);
		}
		
		// 使用更精确的选择器
		const groupElements = item.querySelectorAll('.lak-space-item');
		if (!groupElements || groupElements.length === 0) return null;
		
		// 使用变量存储长度，避免多次访问length属性
		const len = groupElements.length;
		for (let i = 0; i < len; i++) {
			if (groupElements[i].textContent.trim() === '未分组') {
				selectors.groupElementCache.set(item.__uniqueId, groupElements[i]);
				return groupElements[i];
			}
		}
		return null;
	}

	// 优化正则表达式，预编译以提高性能
	const COMMISSION_REGEX = /佣金(\d+(?:\.\d+)?)%/;
	
	// 计算佣金
	function calculateCommission(price, commissionElement) {
		if (!commissionElement) return { commission: 0, hasCommission: false };
		
		const commissionText = commissionElement.textContent;
		const commissionMatch = COMMISSION_REGEX.exec(commissionText);
		
		if (commissionMatch && commissionMatch[1]) {
			const commissionRate = parseFloat(commissionMatch[1]) / 100;
			return { 
				commission: price * commissionRate, 
				hasCommission: true 
			};
		}
		
		return { commission: 0, hasCommission: false };
	}

	// 为单个商品项添加AI手卡按钮
	function addSmartCardButtonToItem(productItem) {
		if (!productItem) return;
		
		// 查找该商品的菜单列表
		const menuList = productItem.querySelector('.list-item-menu-list');
		if (!menuList) return;
		
		// 检查这个菜单是否已经有了我们的AI手卡按钮
		if (menuList.querySelector('.smart-card-btn')) {
			return; // 已存在则跳过
		}

		// 获取产品ID - 查找可能的数据属性
		let productId = '';
		if (productItem.hasAttribute('data-item-id')) {
			productId = productItem.getAttribute('data-item-id');
		} else if (productItem.querySelector('[data-rbd-draggable-id]')) {
			productId = productItem.querySelector('[data-rbd-draggable-id]').getAttribute('data-rbd-draggable-id');
		}

		// 获取产品标题
		let productTitle = '';
		const titleElement = productItem.querySelector('.list-item-content-title-item-name');
		if (titleElement) {
			productTitle = titleElement.textContent;
		}

		// 找到需要插入按钮的位置 - 通常是在"开始讲解"或"再次讲解"按钮之后
		const referenceButton = menuList.querySelector('#mark-time-point button');
		const ellipsisButton = menuList.querySelector('.ellipsis-btn');

		if (referenceButton && ellipsisButton) {
			// 创建新的AI手卡按钮
			const smartCardButton = document.createElement('button');
			smartCardButton.type = 'button';
			smartCardButton.className = 'tbla-btn css-1pg9a38 tbla-btn-text tbla-btn-sm tui-btn tui-btn-fourth tui-btn-small tui-btn-fourth-small tui-btn smart-card-btn';
			smartCardButton.innerHTML = '<span>AI手卡</span>';
			// 使用!important强制应用内联样式
			smartCardButton.setAttribute('style', 'background-color: #1890ff73 !important; color: white !important; border: none !important;');

			// 或者添加一个独特的类名，便于CSS定位
			smartCardButton.classList.add('custom-smart-card-btn');

			// 存储当前产品信息
			if (productId) {
				smartCardButton.setAttribute('data-product-id', productId);
			}
			if (productTitle) {
				smartCardButton.setAttribute('data-product-title', productTitle);
			}

			// 点击AI手卡按钮的处理
			smartCardButton.addEventListener('click', function (e) {
				e.preventDefault();
				e.stopPropagation();

				// 使用菜单按钮打开下拉菜单
				ellipsisButton.click();

				// 等待菜单打开
				setTimeout(() => {
					// 找到打开的菜单
					const dropdowns = document.querySelectorAll('.tbla-dropdown');
					const openDropdown = Array.from(dropdowns).find(dropdown => {
						return dropdown.offsetParent !== null &&
							window.getComputedStyle(dropdown).display !== 'none' &&
							window.getComputedStyle(dropdown).visibility !== 'hidden';
					});

					if (openDropdown) {
						// 在菜单中找到AI手卡选项
						const menuItems = openDropdown.querySelectorAll('.tbla-dropdown-menu-item');
						let smartCardMenuItem = null;

						for (let item of menuItems) {
							if (item.textContent.includes('AI手卡') || item.textContent.includes('智能手卡')) {
								smartCardMenuItem = item;
								break;
							}
						}

						if (smartCardMenuItem) {
							// 点击AI手卡菜单项
							const button = smartCardMenuItem.querySelector('button');
							if (button) {
								button.click();
							}
						}
					}
				}, 300);
			});

			// 插入新按钮到菜单中适当的位置
			if (referenceButton.nextSibling) {
				menuList.insertBefore(smartCardButton, ellipsisButton);
			} else {
				menuList.appendChild(smartCardButton);
			}
		}
	}

	// 批量处理DOM更新，避免频繁重排和重绘
	function batchProcessItems(items) {
		// 如果没有元素需要处理，则直接返回
		if (!items || items.length === 0) return;
		
		// 创建文档片段存储所有的DOM更新
		const updates = [];
		const len = items.length;
		
		for (let i = 0; i < len; i++) {
			const item = items[i];
			
			// 跳过已处理的元素
			if (processedElements.has(item) || item.querySelector('.calculated-commission')) {
				continue;
			}
			
			// 查找价格元素
			const priceElement = findElement(item, '.list-item-content-price', selectors.priceElementCache);
			if (!priceElement) continue;
			
			// 查找佣金信息元素
			const commissionElement = findElement(item, '.list-item-tcp-money', selectors.commissionElementCache);
			
			// 查找分组元素
			const groupElement = findGroupElement(item);
			if (!groupElement) continue;
			
			// 获取价格
			const priceText = priceElement.textContent.trim();
			const price = parseFloat(priceText.replace('￥', ''));
			if (isNaN(price)) continue;
			
			// 计算佣金
			const { commission, hasCommission } = calculateCommission(price, commissionElement);
			
			// 记录需要的更新
			updates.push({
				item,
				groupElement,
				fragment: createCommissionElement(commission, hasCommission)
			});
			
			// 找到对应的list-item容器，同时为该商品添加AI手卡按钮
			const productItem = item.closest('.list-item');
			if (productItem) {
				addSmartCardButtonToItem(productItem);
			}
			
			// 标记为已处理
			processedElements.add(item);
		}
		
		// 批量应用DOM更新
		if (updates.length > 0) {
			// 推迟执行DOM更新，减少布局抖动
			requestAnimationFrame(() => {
				updates.forEach(update => {
					try {
						const parentElement = update.groupElement.parentElement;
						if (parentElement) {
							parentElement.appendChild(update.fragment);
						}
					} catch (error) {
						console.error('插入佣金显示元素时出错:', error);
					}
				});
			});
		}
	}

	// 优化计算佣金函数
	function calculateAndDisplayCommission() {
		// 懒加载并缓存列表容器
		if (!selectors.listContainer) {
			selectors.listContainer = document.querySelector('.list-container') || document.body;
		}
		
		// 只查找未处理的商品项，使用更精确的选择器减少DOM查询范围
		const listItems = selectors.listContainer.querySelectorAll('.list-item-content');
		
		// 批量处理获取的元素
		batchProcessItems(listItems);
	}

	// 使用优化的防抖函数
	const debouncedCalculate = debounce(calculateAndDisplayCommission, 300, 1000);

	// 初始运行计算佣金功能，使用requestIdleCallback优化性能
	if (window.requestIdleCallback) {
		requestIdleCallback(() => calculateAndDisplayCommission(), { timeout: 1000 });
	} else {
		setTimeout(calculateAndDisplayCommission, 1000);
	}

	// 优化MutationObserver配置和回调函数
	const observer = new MutationObserver((mutations) => {
		// 使用更高效的方式检测变化
		let hasRelevantChanges = false;
		let relevantNodes = [];
		
		for (let i = 0; i < mutations.length; i++) {
			const mutation = mutations[i];
			
			// 只处理添加节点和节点属性变化
			if (mutation.type === 'childList' && mutation.addedNodes.length) {
				const nodesArray = Array.from(mutation.addedNodes);
				// 过滤出元素节点
				const elementNodes = nodesArray.filter(node => 
					node.nodeType === 1 && node.tagName
				);
				
				if (elementNodes.length > 0) {
					hasRelevantChanges = true;
					// 收集相关节点
					for (let j = 0; j < elementNodes.length; j++) {
						const node = elementNodes[j];
						// 检查是否是商品列表项或包含商品列表项
						if (node.classList && (
							node.classList.contains('list-item') ||
							node.classList.contains('list-item-content')
						)) {
							relevantNodes.push(node);
						} else {
							// 检查子元素
							const listItems = node.querySelectorAll('.list-item-content');
							if (listItems.length > 0) {
								hasRelevantChanges = true;
								// 将NodeList转换为数组并合并
								relevantNodes = relevantNodes.concat(Array.from(listItems));
							}
						}
					}
				}
			}
			
			// 如果已经确定有变化，可以提前终止循环
			if (hasRelevantChanges && relevantNodes.length > 10) {
				break;
			}
		}
		
		// 只有在有新增商品项时才执行计算
		if (hasRelevantChanges) {
			if (relevantNodes.length > 0) {
				// 先处理已经识别出的节点
				batchProcessItems(relevantNodes);
			}
			// 再做一次完整扫描以确保所有元素都被处理
			debouncedCalculate();
		}
	});

	// 监视商品列表容器
	// 懒加载列表容器
	function initObserver() {
		const listContainer = document.querySelector('.list-container');
		const targetNode = listContainer || document.body;
		
		// 缓存容器引用
		selectors.listContainer = targetNode;
		
		// 使用更精确的observer配置
		observer.observe(targetNode, {
			childList: true, // 监听子节点变化
			subtree: true,   // 监听所有后代节点变化
			// 不监听属性和字符数据变化，减少不必要的触发
			attributes: false,
			characterData: false
		});
	}
	
	// 在页面加载后初始化observer
	if (document.readyState === 'loading') {
		document.addEventListener('DOMContentLoaded', initObserver);
	} else {
		initObserver();
	}
})();
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// MD5哈希函数实现
function md5(string) {
    function RotateLeft(lValue, iShiftBits) {
        return (lValue<<iShiftBits) | (lValue>>>(32-iShiftBits));
    }
    function AddUnsigned(lX,lY) {
        var lX4,lY4,lX8,lY8,lResult;
        lX8 = (lX & 0x80000000);
        lY8 = (lY & 0x80000000);
        lX4 = (lX & 0x40000000);
        lY4 = (lY & 0x40000000);
        lResult = (lX & 0x3FFFFFFF)+(lY & 0x3FFFFFFF);
        if (lX4 & lY4) {
            return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
        }
        if (lX4 | lY4) {
            if (lResult & 0x40000000) {
                return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
            } else {
                return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
            }
        } else {
            return (lResult ^ lX8 ^ lY8);
        }
    }
    function F(x,y,z) { return (x & y) | ((~x) & z); }
    function G(x,y,z) { return (x & z) | (y & (~z)); }
    function H(x,y,z) { return (x ^ y ^ z); }
    function I(x,y,z) { return (y ^ (x | (~z))); }
    function FF(a,b,c,d,x,s,ac) {
        a = AddUnsigned(a, AddUnsigned(AddUnsigned(F(b, c, d), x), ac));
        return AddUnsigned(RotateLeft(a, s), b);
    };
    function GG(a,b,c,d,x,s,ac) {
        a = AddUnsigned(a, AddUnsigned(AddUnsigned(G(b, c, d), x), ac));
        return AddUnsigned(RotateLeft(a, s), b);
    };
    function HH(a,b,c,d,x,s,ac) {
        a = AddUnsigned(a, AddUnsigned(AddUnsigned(H(b, c, d), x), ac));
        return AddUnsigned(RotateLeft(a, s), b);
    };
    function II(a,b,c,d,x,s,ac) {
        a = AddUnsigned(a, AddUnsigned(AddUnsigned(I(b, c, d), x), ac));
        return AddUnsigned(RotateLeft(a, s), b);
    };
    function ConvertToWordArray(string) {
        var lWordCount;
        var lMessageLength = string.length;
        var lNumberOfWords_temp1=lMessageLength + 8;
        var lNumberOfWords_temp2=(lNumberOfWords_temp1-(lNumberOfWords_temp1 % 64))/64;
        var lNumberOfWords = (lNumberOfWords_temp2+1)*16;
        var lWordArray=Array(lNumberOfWords-1);
        var lBytePosition = 0;
        var lByteCount = 0;
        while ( lByteCount < lMessageLength ) {
            lWordCount = (lByteCount-(lByteCount % 4))/4;
            lBytePosition = (lByteCount % 4)*8;
            lWordArray[lWordCount] = (lWordArray[lWordCount] | (string.charCodeAt(lByteCount)<<lBytePosition));
            lByteCount++;
        }
        lWordCount = (lByteCount-(lByteCount % 4))/4;
        lBytePosition = (lByteCount % 4)*8;
        lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80<<lBytePosition);
        lWordArray[lNumberOfWords-2] = lMessageLength<<3;
        lWordArray[lNumberOfWords-1] = lMessageLength>>>29;
        return lWordArray;
    };
    function WordToHex(lValue) {
        var WordToHexValue="",WordToHexValue_temp="",lByte,lCount;
        for (lCount = 0;lCount<=3;lCount++) {
            lByte = (lValue>>>(lCount*8)) & 255;
            WordToHexValue_temp = "0" + lByte.toString(16);
            WordToHexValue = WordToHexValue + WordToHexValue_temp.substr(WordToHexValue_temp.length-2,2);
        }
        return WordToHexValue;
    };
    function Utf8Encode(string) {
        string = string.replace(/\r\n/g,"\n");
        var utftext = "";
        for (var n = 0; n < string.length; n++) {
            var c = string.charCodeAt(n);
            if (c < 128) {
                utftext += String.fromCharCode(c);
            }
            else if((c > 127) && (c < 2048)) {
                utftext += String.fromCharCode((c >> 6) | 192);
                utftext += String.fromCharCode((c & 63) | 128);
            }
            else {
                utftext += String.fromCharCode((c >> 12) | 224);
                utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                utftext += String.fromCharCode((c & 63) | 128);
            }
        }
        return utftext;
    };
    var x=Array();
    var k,AA,BB,CC,DD,a,b,c,d;
    var S11=7, S12=12, S13=17, S14=22;
    var S21=5, S22=9 , S23=14, S24=20;
    var S31=4, S32=11, S33=16, S34=23;
    var S41=6, S42=10, S43=15, S44=21;
    string = Utf8Encode(string);
    x = ConvertToWordArray(string);
    a = 0x67452301; b = 0xEFCDAB89; c = 0x98BADCFE; d = 0x10325476;
    for (k=0;k<x.length;k+=16) {
        AA=a; BB=b; CC=c; DD=d;
        a=FF(a,b,c,d,x[k+0], S11,0xD76AA478);
        d=FF(d,a,b,c,x[k+1], S12,0xE8C7B756);
        c=FF(c,d,a,b,x[k+2], S13,0x242070DB);
        b=FF(b,c,d,a,x[k+3], S14,0xC1BDCEEE);
        a=FF(a,b,c,d,x[k+4], S11,0xF57C0FAF);
        d=FF(d,a,b,c,x[k+5], S12,0x4787C62A);
        c=FF(c,d,a,b,x[k+6], S13,0xA8304613);
        b=FF(b,c,d,a,x[k+7], S14,0xFD469501);
        a=FF(a,b,c,d,x[k+8], S11,0x698098D8);
        d=FF(d,a,b,c,x[k+9], S12,0x8B44F7AF);
        c=FF(c,d,a,b,x[k+10],S13,0xFFFF5BB1);
        b=FF(b,c,d,a,x[k+11],S14,0x895CD7BE);
        a=FF(a,b,c,d,x[k+12],S11,0x6B901122);
        d=FF(d,a,b,c,x[k+13],S12,0xFD987193);
        c=FF(c,d,a,b,x[k+14],S13,0xA679438E);
        b=FF(b,c,d,a,x[k+15],S14,0x49B40821);
        a=GG(a,b,c,d,x[k+1], S21,0xF61E2562);
        d=GG(d,a,b,c,x[k+6], S22,0xC040B340);
        c=GG(c,d,a,b,x[k+11],S23,0x265E5A51);
        b=GG(b,c,d,a,x[k+0], S24,0xE9B6C7AA);
        a=GG(a,b,c,d,x[k+5], S21,0xD62F105D);
        d=GG(d,a,b,c,x[k+10],S22,0x2441453);
        c=GG(c,d,a,b,x[k+15],S23,0xD8A1E681);
        b=GG(b,c,d,a,x[k+4], S24,0xE7D3FBC8);
        a=GG(a,b,c,d,x[k+9], S21,0x21E1CDE6);
        d=GG(d,a,b,c,x[k+14],S22,0xC33707D6);
        c=GG(c,d,a,b,x[k+3], S23,0xF4D50D87);
        b=GG(b,c,d,a,x[k+8], S24,0x455A14ED);
        a=GG(a,b,c,d,x[k+13],S21,0xA9E3E905);
        d=GG(d,a,b,c,x[k+2], S22,0xFCEFA3F8);
        c=GG(c,d,a,b,x[k+7], S23,0x676F02D9);
        b=GG(b,c,d,a,x[k+12],S24,0x8D2A4C8A);
        a=HH(a,b,c,d,x[k+5], S31,0xFFFA3942);
        d=HH(d,a,b,c,x[k+8], S32,0x8771F681);
        c=HH(c,d,a,b,x[k+11],S33,0x6D9D6122);
        b=HH(b,c,d,a,x[k+14],S34,0xFDE5380C);
        a=HH(a,b,c,d,x[k+1], S31,0xA4BEEA44);
        d=HH(d,a,b,c,x[k+4], S32,0x4BDECFA9);
        c=HH(c,d,a,b,x[k+7], S33,0xF6BB4B60);
        b=HH(b,c,d,a,x[k+10],S34,0xBEBFBC70);
        a=HH(a,b,c,d,x[k+13],S31,0x289B7EC6);
        d=HH(d,a,b,c,x[k+0], S32,0xEAA127FA);
        c=HH(c,d,a,b,x[k+3], S33,0xD4EF3085);
        b=HH(b,c,d,a,x[k+6], S34,0x4881D05);
        a=HH(a,b,c,d,x[k+9], S31,0xD9D4D039);
        d=HH(d,a,b,c,x[k+12],S32,0xE6DB99E5);
        c=HH(c,d,a,b,x[k+15],S33,0x1FA27CF8);
        b=HH(b,c,d,a,x[k+2], S34,0xC4AC5665);
        a=II(a,b,c,d,x[k+0], S41,0xF4292244);
        d=II(d,a,b,c,x[k+7], S42,0x432AFF97);
        c=II(c,d,a,b,x[k+14],S43,0xAB9423A7);
        b=II(b,c,d,a,x[k+5], S44,0xFC93A039);
        a=II(a,b,c,d,x[k+12],S41,0x655B59C3);
        d=II(d,a,b,c,x[k+3], S42,0x8F0CCC92);
        c=II(c,d,a,b,x[k+10],S43,0xFFEFF47D);
        b=II(b,c,d,a,x[k+1], S44,0x85845DD1);
        a=II(a,b,c,d,x[k+8], S41,0x6FA87E4F);
        d=II(d,a,b,c,x[k+15],S42,0xFE2CE6E0);
        c=II(c,d,a,b,x[k+6], S43,0xA3014314);
        b=II(b,c,d,a,x[k+13],S44,0x4E0811A1);
        a=II(a,b,c,d,x[k+4], S41,0xF7537E82);
        d=II(d,a,b,c,x[k+11],S42,0xBD3AF235);
        c=II(c,d,a,b,x[k+2], S43,0x2AD7D2BB);
        b=II(b,c,d,a,x[k+9], S44,0xEB86D391);
        a=AddUnsigned(a,AA);
        b=AddUnsigned(b,BB);
        c=AddUnsigned(c,CC);
        d=AddUnsigned(d,DD);
    }
    return (WordToHex(a)+WordToHex(b)+WordToHex(c)+WordToHex(d)).toLowerCase();
}

// ===================== 手卡信息获取功能 =====================

// 获取URL参数的工具函数
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// 获取当前页面的liveId
function getCurrentLiveId() {
    // 从URL参数中获取liveId
    const liveId = getUrlParameter('liveId');
    if (liveId) {
        logDebug('从URL参数获取liveId:', liveId);
        return liveId;
    }
    
    // 如果URL参数中没有，尝试从页面元素中获取
    const liveIdElement = document.querySelector('[data-live-id]');
    if (liveIdElement) {
        const elementLiveId = liveIdElement.getAttribute('data-live-id');
        logDebug('从页面元素获取liveId:', elementLiveId);
        return elementLiveId;
    }
    
    // 默认值（如果都找不到）
    logDebug('未找到liveId，使用默认值');
    return null;
}

// 自定义手卡Modal管理器
class CustomCardModalManager {
    constructor() {
        this.modal = null;
        this.contentArea = null;
        this.currentCardInfo = '';
        this.createModal();
    }

    createModal() {
        // 创建模态框容器
        this.modal = document.createElement('div');
        this.modal.id = 'custom-card-modal';
        this.modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        `;

        // 创建模态框内容
        this.content = document.createElement('div');
        this.content.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 95%;
            max-width: 1400px;
            height: 80%;
            max-height: 700px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        `;

        // 创建标题栏
        this.header = document.createElement('div');
        this.header.style.cssText = `
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        `;

        this.title = document.createElement('div');
        this.title.innerHTML = 'AI手卡';
        this.title.style.cssText = `
            font-size: 16px;
            font-weight: 500;
            color: #333;
        `;

        this.closeButton = document.createElement('button');
        this.closeButton.innerHTML = '×';
        this.closeButton.style.cssText = `
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            color: #666;
            font-size: 20px;
            font-weight: normal;
            font-family: Arial, sans-serif;
            cursor: pointer;
            padding: 0;
            margin: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            transition: all 0.2s ease;
            line-height: 1;
            text-align: center;
            vertical-align: middle;
        `;

        this.closeButton.onmouseover = () => {
            this.closeButton.style.backgroundColor = '#e6e6e6';
            this.closeButton.style.borderColor = '#bfbfbf';
        };

        this.closeButton.onmouseout = () => {
            this.closeButton.style.backgroundColor = '#f5f5f5';
            this.closeButton.style.borderColor = '#d9d9d9';
        };

        this.header.appendChild(this.title);
        this.header.appendChild(this.closeButton);

        // 创建内容区域
        this.body = document.createElement('div');
        this.body.style.cssText = `
            flex: 1;
            overflow-y: auto;
            background: white;
        `;

        // 创建手卡内容显示区域
        this.cardContent = document.createElement('div');
        this.cardContent.style.cssText = `
            padding: 20px;
            line-height: 1.8;
            font-size: 24px;
            color: #1c1e21;
            font-family: PingFang SC, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            white-space: pre-wrap;
            word-wrap: break-word;
        `;

        this.body.appendChild(this.cardContent);

        // 创建底部按钮区域
        this.footer = document.createElement('div');
        this.footer.style.cssText = `
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            padding: 16px 20px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            flex-shrink: 0;
        `;

        // 创建底部关闭按钮
        this.bottomCloseButton = document.createElement('button');
        this.bottomCloseButton.innerHTML = '关闭';
        this.bottomCloseButton.style.cssText = `
            background: #dc3545;
            border: 1px solid #dc3545;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            padding: 8px 16px;
            border-radius: 4px;
            transition: all 0.2s ease;
            min-width: 80px;
        `;

        this.bottomCloseButton.onmouseover = () => {
            this.bottomCloseButton.style.backgroundColor = '#c82333';
            this.bottomCloseButton.style.borderColor = '#c82333';
        };

        this.bottomCloseButton.onmouseout = () => {
            this.bottomCloseButton.style.backgroundColor = '#dc3545';
            this.bottomCloseButton.style.borderColor = '#dc3545';
        };

        this.footer.appendChild(this.bottomCloseButton);

        // 组装模态框
        this.content.appendChild(this.header);
        this.content.appendChild(this.body);
        this.content.appendChild(this.footer);
        this.modal.appendChild(this.content);

        // 添加到页面
        document.body.appendChild(this.modal);

        // 绑定事件
        this.bindEvents();
    }

    bindEvents() {
        // 关闭按钮事件
        this.closeButton.addEventListener('click', () => {
            // 调用清除协议
            sendClearProtocol();
            this.hide();
        });

        // 点击背景关闭
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
				sendClearProtocol();
                this.hide();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.style.display === 'flex') {
				sendClearProtocol();
                this.hide();
            }
        });

        // 底部关闭按钮事件
        this.bottomCloseButton.addEventListener('click', () => {
            // 调用清除协议
            sendClearProtocol();
            this.hide();
        });
    }

    show() {
        if (this.modal) {
            this.modal.style.display = 'block';
            this.modal.style.opacity = '1';
            this.modal.style.transform = 'scale(1)';
        }
    }

    hide() {
        if (this.modal) {
            this.modal.style.transition = 'all 0.3s ease';
            this.modal.style.opacity = '0';
            this.modal.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.modal.style.display = 'none';
            }, 300);
        }
    }

    updateCardInfo(cardInfo, isComplete = false, isError = false, useCache = false, isHtml = false) {
        if (!this.cardContent) return;

        if (isError) {
            this.cardContent.innerHTML = `
                <div style="
                    text-align: center; 
                    color: #dc3545; 
                    padding: 40px 20px;
                ">
                    <div style="font-size: 48px; margin-bottom: 16px;">❌</div>
                    <div style="font-size: 16px;">${cardInfo}</div>
                </div>
            `;
        } else if (useCache) {
            // 如果是缓存数据，直接显示完整内容，不使用任何效果
            this.isStreamMode = false;
            if (isHtml) {
                this.cardContent.innerHTML = cardInfo;
            } else {
                this.cardContent.textContent = cardInfo;
            }
        } else {
            // 流数据实时更新，使用打字机效果
            this.typewriterEffect(cardInfo);
        }

        // 滚动到底部
        this.scrollToBottom();
    }

    // 处理流数据实时更新
    updateStreamContent(text) {
        // 清理之前的打字机定时器
        if (this.typewriterTimer) {
            clearTimeout(this.typewriterTimer);
            this.typewriterTimer = null;
        }

        // 直接显示内容，不使用打字机效果
        this.cardContent.textContent = text;
    }

    // 简化的打字机效果
    typewriterEffect(text) {
        // 如果这是第一次调用或者内容为空，初始化
        if (!this.isStreamMode || this.cardContent.textContent === '') {
            this.initializeStreamMode();
        }
        
        // 更新流内容
        this.updateStreamText(text);
    }

    // 初始化流模式
    initializeStreamMode() {
        this.isStreamMode = true;
        this.cardContent.innerHTML = '';
        
        // 创建文本容器
        this.textContainer = document.createElement('span');
        this.cardContent.appendChild(this.textContainer);
        
        // 创建光标
        this.cursor = document.createElement('span');
        this.cursor.style.cssText = `
            display: inline-block;
            width: 2px;
            height: 1em;
            background-color: #007bff;
            margin-left: 1px;
            animation: blink 1s infinite;
        `;
        
        // 添加光标动画CSS
        if (!document.getElementById('cursor-animation-simple')) {
            const style = document.createElement('style');
            style.id = 'cursor-animation-simple';
            style.textContent = `
                @keyframes blink {
                    0%, 50% { opacity: 1; }
                    51%, 100% { opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
        
        this.cardContent.appendChild(this.cursor);
    }

    // 更新流文本内容
    updateStreamText(newText) {
        if (!this.textContainer) return;
        
        // 直接更新文本内容
        this.textContainer.textContent = newText;
        
        // 确保光标在末尾
        if (this.cursor && this.cursor.parentNode !== this.cardContent) {
            this.cardContent.appendChild(this.cursor);
        }
        
        // 滚动到底部
        this.scrollToBottom();
    }

    // 完成流模式，移除光标
    finishStreamMode() {
        this.isStreamMode = false;
        if (this.cursor && this.cursor.parentNode) {
            this.cursor.parentNode.removeChild(this.cursor);
            this.cursor = null;
        }
    }

    // 完成流模式并高亮显示关键词
    finishStreamAndHighlight(highlightedHtml) {
        // 移除光标
        this.finishStreamMode();
        
        // 用高亮的HTML内容覆盖显示
        this.cardContent.innerHTML = highlightedHtml;
        
        // 滚动到底部
        this.scrollToBottom();
    }

    scrollToBottom() {
        if (this.body) {
            this.body.scrollTop = this.body.scrollHeight;
        }
    }

    destroy() {
        // 清理打字机定时器
        if (this.typewriterTimer) {
            clearTimeout(this.typewriterTimer);
            this.typewriterTimer = null;
        }

        // 清理流模式
        this.isStreamMode = false;
        this.textContainer = null;
        this.cursor = null;

        if (this.modal && this.modal.parentNode) {
            this.modal.parentNode.removeChild(this.modal);
        }
        this.modal = null;
        this.content = null;
        this.header = null;
        this.body = null;
        this.cardContent = null;
        this.closeButton = null;
        this.title = null;
        this.footer = null;
        this.bottomCloseButton = null;
    }

    // 更新手卡标题，显示产品信息
    updateTitle(productInfo) {
        if (!this.title || !productInfo) return;
        
        let titleText = 'AI手卡';
        
        // 添加产品标题
        if (productInfo.title) {
            titleText = productInfo.title;
        }
        
        // 添加佣金信息
        if (productInfo.commission) {
            titleText += ` | ${productInfo.commission}`;
        }
        
        this.title.innerHTML = titleText;
        this.title.title = titleText; // 添加tooltip显示完整标题
        
        // 如果标题过长，添加省略号样式
        this.title.style.cssText = `
            font-size: 16px;
            font-weight: 500;
            color: #333;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 10px;
        `;
    }
}

// 手卡信息获取器
class CustomCardClient {
    constructor() {
        this.appKey = "12574478";
        this.jsv = "2.7.4";
        this.apiName = "mtop.tblive.portal.item.card.user.token";
        this.apiVersion = "1.0";
        this.modal = new CustomCardModalManager();
    }

    // 获取h5_tk token用于签名
    getH5Token() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === '_m_h5_tk') {
                return value.split('_')[0];
            }
        }
        return "";
    }

    // 获取用户token
    async getUserToken(liveId) {
        const timestamp = Date.now().toString();
        const dataStr = JSON.stringify({"liveId": liveId});
        
        const h5Token = this.getH5Token();
        const signString = `${h5Token}&${timestamp}&${this.appKey}&${dataStr}`;
        const sign = md5(signString);
        
        const params = new URLSearchParams({
            'jsv': this.jsv,
            'appKey': this.appKey,
            't': timestamp,
            'sign': sign,
            'api': this.apiName,
            'v': this.apiVersion,
            'preventFallback': 'true',
            'type': 'jsonp',
            'dataType': 'jsonp',
            'callback': 'mtopjsonp189',
            'data': dataStr
        });
        
        const url = `https://h5api.m.taobao.com/h5/${this.apiName}/${this.apiVersion}/?${params}`;
        
        try {
            const response = await fetch(url, {
                method: 'GET',
                credentials: 'include'
            });
            
            if (response.ok) {
                const text = await response.text();
                const match = text.match(/mtopjsonp189\((.*)\)/);
                if (match) {
                    const data = JSON.parse(match[1]);
                    if (data.ret && data.ret[0].includes('SUCCESS')) {
                        const token = data.data?.result;
                        if (token) {
                            logDebug(`手卡Token: ${token}`);
                            return token;
                        }
                    }
                }
            }
        } catch (error) {
            console.error('获取手卡token失败:', error);
        }
        return null;
    }

    // 获取商品信息（从页面中提取）
    getProductInfo(listItem) {
        let productInfo = {
            itemId: '',
            title: '',
            price: '',
            commission: ''
        };

        try {
            logDebug('=== 获取商品信息开始 ===');
            
            // 获取商品ID - 直接从data-rbd-draggable-id获取
            const draggableElement = listItem.querySelector('[data-rbd-drag-handle-draggable-id]');
            if (draggableElement) {
                productInfo.itemId = draggableElement.getAttribute('data-rbd-drag-handle-draggable-id');
                logDebug('从data-rbd-drag-handle-draggable-id获取到itemId:', productInfo.itemId);
            }

            // 获取商品标题 - 直接从list-item-content-title-item-name获取
            const titleElement = listItem.querySelector('.list-item-content-title-item-name');
            if (titleElement) {
                productInfo.title = titleElement.textContent.trim();
                logDebug('获取到商品标题:', productInfo.title);
            }

            // 获取佣金信息 - 直接从calculated-commission获取
            const commissionElement = listItem.querySelector('.calculated-commission');
            if (commissionElement) {
                productInfo.commission = commissionElement.textContent.trim();
                logDebug('获取到佣金信息:', productInfo.commission);
            }

            logDebug('=== 最终获取到的商品信息 ===');
            logDebug('itemId:', productInfo.itemId);
            logDebug('title:', productInfo.title);
            logDebug('commission:', productInfo.commission);

        } catch (error) {
            console.error('获取商品信息失败:', error);
        }

        return productInfo;
    }

    // 获取手卡信息
    async getCardInfo(productInfo, liveId) {
        // 首先更新手卡标题，显示产品信息
        this.modal.updateTitle(productInfo);
        
        const token = await this.getUserToken(liveId);
        if (!token) {
            logDebug("获取手卡token失败");
            this.modal.updateCardInfo("获取token失败，无法获取手卡信息", true, true, false, false);
            return;
        }
        
        // 验证必要参数
        if (!productInfo.itemId) {
            logDebug("未找到商品ID");
            this.modal.updateCardInfo("未找到商品ID，无法获取手卡信息", true, true, false, false);
            return;
        }
        
        logDebug('使用的参数:', {
            itemId: productInfo.itemId,
            liveId: liveId,
            title: productInfo.title,
            price: productInfo.price,
            commission: productInfo.commission
        });
        
        // 构建手卡请求参数
        const params = new URLSearchParams({
            'itemId': productInfo.itemId,
            'liveId': liveId,
            'type': '1',
            'subType': '1',
            'isRenew': 'false',
            'scene': 'normal',
            'reqKey': token,
            'sceneCode': 'beforeLivebatch',
            'trackId': Math.random().toString(36).substr(2, 9),
            'isFilterSavedScript': 'true'
        });
        
        const url = `https://tblive.taobao.com/api/sse/script/query?${params}`;
        
        try {
            const response = await fetch(url, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache'
                }
            });
            
            if (!response.ok) {
                logDebug(`手卡SSE连接失败: ${response.status}`);
                this.modal.updateCardInfo(`连接失败: ${response.status}`, true, true, false, false);
                return;
            }
            
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let fullScript = '';
            
            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');
                    
                    for (let line of lines) {
                        if (line.trim()) {
                            const [dataType, content] = this.parseSSEData(line);
                            
                            if (dataType === 'data' && content) {
                                try {
                                    const jsonData = JSON.parse(content);
                                    
                                    const useCache = jsonData.useCache || false;
                                    const finished = jsonData.finished || false;
                                    const script = jsonData.script || '';
                                    const keywordList = jsonData.keywordList || [];
                                    
                                    if (script) {
                                        fullScript = script;
                                        
                                        if (useCache) {
                                            // 缓存数据，检查是否有关键词需要高亮
                                            if (keywordList.length > 0) {
                                                const highlightedScript = this.highlightKeywords(script, keywordList);
                                                this.modal.updateCardInfo(highlightedScript, false, false, true, true); // 最后一个参数表示是HTML
                                            } else {
                                                this.modal.updateCardInfo(script, false, false, true);
                                            }
                                            return; // 完成后退出
                                        } else if (finished && keywordList.length > 0) {
                                            // 流数据完成且有关键词，高亮显示关键词后覆盖
                                            const highlightedScript = this.highlightKeywords(script, keywordList);
                                            this.modal.finishStreamAndHighlight(highlightedScript);
                                            return; // 完成后退出，避免继续执行流数据逻辑
                                        } else {
                                            // 流数据实时更新，使用打字机效果
                                            this.modal.updateCardInfo(script, false, false, false, false);
                                        }
                                    }
                                    
                                } catch (e) {
                                    logDebug(`[手卡原始数据] ${content}`);
                                }
                            }
                        }
                    }
                }
                
                // 如果循环结束但没有获取到完整脚本，显示已有内容
                if (fullScript) {
                    this.modal.updateCardInfo(fullScript, true, false, false, false);
                    // 流数据结束，完成流模式
                    this.modal.finishStreamMode();
                } else {
                    this.modal.updateCardInfo("未能获取到手卡信息", true, true, false, false);
                }
                
            } finally {
                reader.releaseLock();
            }
            
        } catch (error) {
            console.error("手卡连接异常:", error);
            this.modal.updateCardInfo(`连接异常: ${error.message}`, true, true, false, false);
            // 错误时也完成流模式
            this.modal.finishStreamMode();
        }
    }

    // 解析SSE数据
    parseSSEData(line) {
        line = line.trim();
        if (!line) return [null, null];
        
        if (line.startsWith('data:')) {
            return line.startsWith('data: ') 
                ? ['data', line.substring(6)] 
                : ['data', line.substring(5)];
        } else if (line.startsWith('event:')) {
            return line.startsWith('event: ') 
                ? ['event', line.substring(7)] 
                : ['event', line.substring(6)];
        } else if (line.startsWith('id:')) {
            return line.startsWith('id: ') 
                ? ['id', line.substring(4)] 
                : ['id', line.substring(3)];
        } else {
            return ['unknown', line];
        }
    }

    // 显示手卡
    showCard() {
        this.modal.show();
    }

    // 隐藏手卡
    hideCard() {
        this.modal.hide();
    }

    // 高亮关键词
    highlightKeywords(text, keywordList) {
        if (!keywordList || keywordList.length === 0) {
            return text;
        }
        
        // 按位置排序，从后往前处理，避免位置偏移
        const sortedKeywords = keywordList.sort((a, b) => b.start - a.start);
        
        let result = text;
        for (const keyword of sortedKeywords) {
            const { start, end, word } = keyword;
            if (start >= 0 && end <= text.length && start < end) {
                const before = result.substring(0, start);
                const highlighted = `<span style="color: #115ffd; font-weight: bold;">${word}</span>`;
                const after = result.substring(end);
                result = before + highlighted + after;
            }
        }
        
        return result;
    }
}

// 全局自定义手卡函数
window.showCustomCard = async function(listItem) {
    // 获取当前页面的liveId
    const liveId = getCurrentLiveId();
    if (!liveId) {
        console.error('无法获取liveId');
        alert('无法获取直播ID，请检查页面URL');
        return;
    }
    
    // 清理之前的手卡modal
    const existingModal = document.getElementById('custom-card-modal');
    if (existingModal) {
        existingModal.remove();
    }
    
    const cardClient = new CustomCardClient();
    const productInfo = cardClient.getProductInfo(listItem);
    
    logDebug('获取到的商品信息:', productInfo);
    logDebug('使用的liveId:', liveId);
    
    // 显示modal
    cardClient.showCard();
    
    // 获取手卡信息
    await cardClient.getCardInfo(productInfo, liveId);
};

