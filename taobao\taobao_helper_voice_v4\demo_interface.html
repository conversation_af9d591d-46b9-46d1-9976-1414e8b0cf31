<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手卡音频播放功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }
        
        .demo-header {
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .demo-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }
        
        .demo-close {
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            color: #666;
            font-size: 20px;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .audio-section {
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .play-button {
            background: #007bff;
            border: 1px solid #007bff;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            padding: 8px 16px;
            border-radius: 4px;
            min-width: 80px;
        }
        
        .progress-container {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .progress-bar {
            flex: 1;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            position: relative;
            cursor: pointer;
        }
        
        .progress-fill {
            height: 100%;
            background: #007bff;
            width: 30%;
            transition: width 0.1s ease;
        }
        
        .time-display {
            font-size: 12px;
            color: #666;
            min-width: 80px;
            text-align: center;
        }
        
        .status-display {
            font-size: 12px;
            color: #007bff;
            font-weight: 500;
        }
        
        .card-content {
            padding: 20px;
            line-height: 1.8;
            font-size: 24px;
            color: #1c1e21;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .demo-footer {
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            padding: 16px 20px;
            display: flex;
            justify-content: flex-end;
        }
        
        .close-button {
            background: #dc3545;
            border: 1px solid #dc3545;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            padding: 8px 16px;
            border-radius: 4px;
            min-width: 80px;
        }
        
        .feature-list {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
        }
        
        .feature-list h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .feature-list ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .feature-list li {
            margin: 8px 0;
            line-height: 1.5;
        }
        
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="feature-list">
        <h3>🎵 新增功能：手卡音频播放</h3>
        <ul>
            <li><strong>播放按钮：</strong>点击播放/暂停音频，支持状态切换</li>
            <li><strong>进度条：</strong>实时显示播放进度，支持点击跳转</li>
            <li><strong>时间显示：</strong>显示当前时间和总时长 (MM:SS / MM:SS)</li>
            <li><strong>状态提示：</strong>显示加载、播放、暂停、完成等状态</li>
            <li><strong>音频路径：</strong>自动加载 D:/voice/itemId.wav 文件</li>
            <li><strong>自动清理：</strong>关闭手卡时自动停止播放</li>
        </ul>
        <div class="highlight">
            <strong>音频文件命名规则：</strong>D:/voice/{商品ID}.wav<br>
            例如：商品ID为12345的音频文件路径为 D:/voice/12345.wav
        </div>
    </div>

    <div class="demo-container">
        <!-- 标题栏 -->
        <div class="demo-header">
            <div class="demo-title">测试商品标题 | 佣金:￥10.50</div>
            <div class="demo-close">×</div>
        </div>
        
        <!-- 音频播放区域 -->
        <div class="audio-section">
            <button class="play-button" onclick="toggleDemo()">⏸️ 暂停</button>
            <div class="progress-container">
                <div class="progress-bar" onclick="seekDemo(event)">
                    <div class="progress-fill" id="demo-progress"></div>
                </div>
                <span class="time-display">01:23 / 04:56</span>
                <span class="status-display">播放中</span>
            </div>
        </div>
        
        <!-- 手卡内容 -->
        <div class="card-content">
这是一个高品质的产品，具有以下特点：

✨ <span style="color: #115ffd; font-weight: bold;">优质材料</span>：采用进口原材料制作
🎯 <span style="color: #115ffd; font-weight: bold;">精美设计</span>：时尚外观，符合现代审美
💰 <span style="color: #115ffd; font-weight: bold;">超值价格</span>：性价比极高，物超所值

推荐理由：
• 用户评价好，满意度高
• 售后服务完善，无忧购买
• 限时优惠，机会难得

立即下单，享受优质购物体验！
        </div>
        
        <!-- 底部按钮 -->
        <div class="demo-footer">
            <button class="close-button">关闭</button>
        </div>
    </div>

    <script>
        let isPlaying = true;
        let progress = 30;
        
        function toggleDemo() {
            const button = document.querySelector('.play-button');
            const status = document.querySelector('.status-display');
            
            if (isPlaying) {
                button.innerHTML = '▶️ 播放';
                button.style.backgroundColor = '#007bff';
                button.style.borderColor = '#007bff';
                status.textContent = '已暂停';
                status.style.color = '#6c757d';
                isPlaying = false;
            } else {
                button.innerHTML = '⏸️ 暂停';
                button.style.backgroundColor = '#dc3545';
                button.style.borderColor = '#dc3545';
                status.textContent = '播放中';
                status.style.color = '#007bff';
                isPlaying = true;
            }
        }
        
        function seekDemo(event) {
            const progressBar = event.currentTarget;
            const rect = progressBar.getBoundingClientRect();
            const clickX = event.clientX - rect.left;
            const newProgress = (clickX / rect.width) * 100;
            
            document.getElementById('demo-progress').style.width = newProgress + '%';
            
            // 模拟时间更新
            const totalSeconds = 4 * 60 + 56; // 4:56
            const currentSeconds = Math.floor((newProgress / 100) * totalSeconds);
            const currentMinutes = Math.floor(currentSeconds / 60);
            const currentSecs = currentSeconds % 60;
            
            const timeDisplay = document.querySelector('.time-display');
            timeDisplay.textContent = `${currentMinutes.toString().padStart(2, '0')}:${currentSecs.toString().padStart(2, '0')} / 04:56`;
        }
        
        // 模拟播放进度更新
        setInterval(() => {
            if (isPlaying && progress < 100) {
                progress += 0.5;
                document.getElementById('demo-progress').style.width = progress + '%';
                
                if (progress >= 100) {
                    const button = document.querySelector('.play-button');
                    const status = document.querySelector('.status-display');
                    button.innerHTML = '▶️ 播放';
                    button.style.backgroundColor = '#007bff';
                    button.style.borderColor = '#007bff';
                    status.textContent = '已播放完毕';
                    status.style.color = '#28a745';
                    isPlaying = false;
                }
            }
        }, 100);
    </script>
</body>
</html>
